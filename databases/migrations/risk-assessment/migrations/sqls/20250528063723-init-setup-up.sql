CREATE SCHEMA main;

SET
    search_path TO main,
    public;
--------------------------Master Tables------------------------
-- Table: main.categories
CREATE TABLE
    main.category (
        id SERIAL PRIMARY KEY,
        name VARCHA<PERSON>(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by <PERSON><PERSON><PERSON><PERSON>(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by <PERSON><PERSON><PERSON><PERSON>(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by <PERSON><PERSON><PERSON><PERSON>(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );
-- Table for Hazard Category
CREATE TABLE
    main.hazard (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by <PERSON><PERSON><PERSON><PERSON>(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by <PERSON><PERSON>HA<PERSON>(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VA<PERSON>HA<PERSON>(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.parameter_type (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        is_required_for_risk_rating BOOLEAN DEFAULT TRUE,
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.parameter (
        id SERIAL PRIMARY KEY,
        parameter_type_id INT REFERENCES main.parameter_type (id),
        name VARCHAR(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.task_reliability_assessment (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255),
        options JSON,
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.approval_required (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255),
        status INT,
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

--------------------------Template Tables------------------------
CREATE TABLE
    main.template (
        id SERIAL PRIMARY KEY,
        tenant_id INT,
        task_requiring_ra VARCHAR(255),
        task_duration VARCHAR(255),
        task_alternative_consideration VARCHAR(4000),
        task_rejection_reason TEXT,
        worst_case_scenario VARCHAR(4000),
        recovery_measures VARCHAR(4000),
        draft_step INT,
        status INT DEFAULT 1 NOT NULL, -- 1 = DRAFT, 2 = PUBLISHED, 3 = INACTIVE
        publish_on TIMESTAMP WITHOUT TIME ZONE,
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.template_category (
        id SERIAL PRIMARY KEY,
        template_id INT REFERENCES main.template (id),
        category_id INT REFERENCES main.category (id),
        category_is_other BOOLEAN DEFAULT FALSE,
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.template_hazard (
        id SERIAL PRIMARY KEY,
        template_id INT REFERENCES main.template (id),
        hazard_id INT REFERENCES main.hazard (id),
        hazard_category_is_other BOOLEAN DEFAULT FALSE,
        value VARCHAR(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.template_parameter (
        id SERIAL PRIMARY KEY,
        template_id INT REFERENCES main.template (id),
        parameter_type_id INT REFERENCES main.parameter_type (id),
        parameter_id INT REFERENCES main.parameter (id),
        parameter_is_other BOOLEAN DEFAULT FALSE,
        value VARCHAR(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.template_job (
        id SERIAL PRIMARY KEY,
        template_id INT REFERENCES main.template (id),
        job_step VARCHAR(255),
        job_hazard VARCHAR(255),
        job_nature_of_risk VARCHAR(255),
        job_existing_control VARCHAR(4000),
        job_additional_mitigation VARCHAR(4000),
        job_close_out_date DATE,
        job_close_out_responsibility_id VARCHAR, -- Assuming this references a user or role
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.template_job_initial_risk_rating (
        id SERIAL PRIMARY KEY,
        template_job_id INT REFERENCES main.template_job (id),
        parameter_type_id INT REFERENCES main.parameter_type (id),
        rating VARCHAR(2), -- Assuming this is a numeric rating
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.template_job_residual_risk_rating (
        id SERIAL PRIMARY KEY,
        template_job_id INT REFERENCES main.template_job (id),
        parameter_type_id INT REFERENCES main.parameter_type (id),
        rating VARCHAR(2),
        reason VARCHAR(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.template_task_reliability_assessment (
        id SERIAL PRIMARY KEY,
        template_id INT REFERENCES main.template (id),
        task_reliability_assessment_id INT REFERENCES main.task_reliability_assessment (id),
        task_reliability_assessment_answer VARCHAR(3),
        condition VARCHAR(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE 
    main.template_keyword(
        id SERIAL PRIMARY KEY,
        template_id INT REFERENCES main.template (id),
        name VARCHAR(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );
-- Add indexes for template search optimization
CREATE INDEX idx_template_task_requiring_ra ON main.template (task_requiring_ra);
CREATE INDEX idx_template_created_at ON main.template (created_at);
CREATE INDEX idx_template_status ON main.template (status);
CREATE INDEX idx_template_created_by ON main.template (created_by);

-- Add indexes for joins
CREATE INDEX idx_template_category_template_id ON main.template_category (template_id);
CREATE INDEX idx_template_category_category_id ON main.template_category (category_id);
CREATE INDEX idx_template_hazard_template_id ON main.template_hazard (template_id);
CREATE INDEX idx_template_hazard_hazard_id ON main.template_hazard (hazard_id);

-- Add indexes for name searches
CREATE INDEX idx_category_name ON main.category (name);
CREATE INDEX idx_hazard_name ON main.hazard (name);

-- Add indexes for keywords and status-based queries
CREATE INDEX idx_template_keyword_name ON main.template_keyword (name);
CREATE INDEX idx_template_keyword_template_id ON main.template_keyword (template_id);

-- Add composite indexes for joins with status
CREATE INDEX idx_template_category_composite ON main.template_category (template_id, category_id, status);
CREATE INDEX idx_template_hazard_composite ON main.template_hazard (template_id, hazard_id, status);
CREATE INDEX idx_template_status_created ON main.template (status, created_at);

--------------------------Risks Tables------------------------
CREATE TABLE
    main.risk (
        id SERIAL PRIMARY KEY,
        tenant_id INT,
        template_id INT REFERENCES main.template (id),
        task_requiring_ra VARCHAR(255),
        assessor int,  --1 as Office 2 as Vessel
        vessel_ownership_id INT,
        vessel_id INT,
        vessel_code VARCHAR(255),
        vessel_name VARCHAR(255),
        vessel_category VARCHAR(255),
        vessel_tech_group VARCHAR(255),
        office_id INT,
        office_name VARCHAR,
        date_risk_assessment DATE,
        risk_rating INT, -- 1 = LOW, 2 = MEDIUM, 3 = HIGH
        task_duration VARCHAR(255),
        ra_level INT, -- 1 = Routine, -- 2 = Special, 3 = Critical, -- 4 = Level 1 RA
        task_alternative_consideration VARCHAR(4000),
        task_rejection_reason TEXT,
        worst_case_scenario VARCHAR(4000),
        recovery_measures VARCHAR(4000),
        approval_date TIMESTAMP WITHOUT TIME ZONE,
        draft_step INT,
        status INT DEFAULT 1 NOT NULL, -- 1 = DRAFT, 2 = PUBLISHED, 3 = APPROVED, 4 = REJECTED, 5 = PENDING, 6 = INACTIVE
        publish_on TIMESTAMP WITHOUT TIME ZONE,
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );


CREATE TABLE main.risk_team_member(
        id SERIAL PRIMARY KEY,
        risk_id INT REFERENCES main.risk (id),
        seafarer_id INT,
        seafarer_person_id INT,
        seafarer_hkid INT,
        seafarer_name VARCHAR(255),
        seafarer_rank VARCHAR(255),
        seafarer_rank_id INT,
        seafarer_rank_sort_order INT,
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
);

CREATE TABLE
    main.risk_category (
        id SERIAL PRIMARY KEY,
        risk_id INT REFERENCES main.risk (id),
        category_id INT REFERENCES main.category (id),
        category_is_other BOOLEAN DEFAULT FALSE,
        value VARCHAR(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.risk_hazard (
        id SERIAL PRIMARY KEY,
        risk_id INT REFERENCES main.risk (id),
        hazard_id INT REFERENCES main.hazard (id),
        hazard_category_is_other BOOLEAN DEFAULT FALSE,
        value VARCHAR(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );


CREATE TABLE
    main.risk_parameter (
        id SERIAL PRIMARY KEY,
        risk_id INT REFERENCES main.risk (id),
        parameter_type_id INT REFERENCES main.parameter_type (id),
        parameter_id INT REFERENCES main.parameter (id),
        parameter_is_other BOOLEAN DEFAULT FALSE,
        value VARCHAR(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.risk_job (
        id SERIAL PRIMARY KEY,
        risk_id INT REFERENCES main.risk (id),
        job_step VARCHAR(255),
        job_hazard VARCHAR(255),
        job_nature_of_risk VARCHAR(255),
        job_existing_control VARCHAR(4000),
        job_additional_mitigation VARCHAR(4000),
        job_close_out_date DATE,
        job_close_out_responsibility_id VARCHAR, -- Assuming this references a user or role
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.risk_job_initial_risk_rating (
        id SERIAL PRIMARY KEY,
        risk_job_id INT REFERENCES main.risk_job (id),
        parameter_type_id INT REFERENCES main.parameter_type (id),
        rating VARCHAR(2), -- Assuming this is a numeric rating
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.risk_job_residual_risk_rating (
        id SERIAL PRIMARY KEY,
        risk_job_id INT REFERENCES main.risk_job (id),
        parameter_type_id INT REFERENCES main.parameter_type (id),
        rating VARCHAR(2),
        reason VARCHAR(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE
    main.risk_task_reliability_assessment (
        id SERIAL PRIMARY KEY,
        risk_id INT REFERENCES main.risk (id),
        task_reliability_assessment_id INT REFERENCES main.task_reliability_assessment (id),
        task_reliability_assessment_answer VARCHAR(3),
        condition VARCHAR(255),
        status INT DEFAULT 1 NOT NULL, -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );

CREATE TABLE main.risk_approver (
        id SERIAL PRIMARY KEY,
        risk_id INT REFERENCES main.risk (id),
        keycloak_id INT,
        user_name VARCHAR(255),
        user_email VARCHAR(100),
        job_title VARCHAR(255),
        message VARCHAR(255),
        approval_status int, --1 as APPROVED, 2 as REJECTED, 3 as CONDITIONALLY APPROVED
        status INT DEFAULT 1 NOT NULL, -- 0 as default -- 1 = ACTIVE, 2 = INACTIVE
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
);

CREATE TABLE
    main.activity_log (
        id SERIAL PRIMARY KEY,
        entity_id INT NOT NULL, -- ID of the risk, template or other entity
        entity_type INT NOT NULL, -- 1 = RISK, 2 = TEMPLATE, etc.
        action_type INT NOT NULL, -- 1 = CREATE, 2 = UPDATE, 3 = DELETE
        parameter JSON, -- JSON to store additional parameters or details about the action
        tenant_id INT,
        created_by VARCHAR(255),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW (),
        updated_by VARCHAR(255),
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        deleted_by VARCHAR(255),
        deleted_at TIMESTAMP WITHOUT TIME ZONE
    );
    

----------------Master data entries-----------------
INSERT INTO main.category (name, created_by)
VALUES
    ('Equipment / Machinery Maintenance', '0000'),
    ('Navigation', '0000'),
    ('Cargo operation-Dry', '0000'),
    ('Hot Work', '0000'),
    ('Cold Work', '0000'),
    ('Anchoring', '0000'),
    ('Equipment / Machinery / System Failure', '0000'),
    ('Cargo Operations - Tankers', '0000'),
    ('Structural Failure / Damage', '0000'),
    ('Potential Breach of SMS', '0000'),
    ('Tasks Requiring Work Permits (Permit to Work System)', '0000'),
    ('Entry into Enclosed Spaces', '0000'),
    ('Working Aloft', '0000'),
    ('Diving Operation', '0000'),
    ('Mooring', '0000'),
    ('Fuel Change Over', '0000'),
    ('Security', '0000'),
    ('Launching of Boats', '0000'),
    ('Maintenance on Ship''s Elevator', '0000'),
    ('CBM Mooring', '0000'),
    ('New Equipment / Modification of Equipment', '0000'),
    ('Deck Maintenance', '0000'),
    ('Critical Equipment Maintenance / Failure', '0000'),
    ('Crewing / Manning', '0000'),
    ('Management of Change (MoC)', '0000'),
    ('Transfer of Personnel', '0000'),
    ('Painting in Enclosed Spaces', '0000'),
    ('Electrical Isolation', '0000'),
    ('Ship to Ship (STS) Operations', '0000'),
    ('Bunkering', '0000'),
    ('Specialized Vessel Operation', '0000'),
    ('SIMOPS', '0000'),
    ('Cyber Security', '0000');


-- Sample data for hazards
INSERT INTO main.hazard (name, created_by)
VALUES
    ('Manual Handling Activities', '0000'),
    ('Loads being transported overhead', '0000'),
    ('Working at Height', '0000'),
    ('Working with Ship''s Rail Lowered', '0000'),
    ('Use of Scientific Equipment on Deck', '0000'),
    ('Use of Electricity on Deck', '0000'),
    ('Inexperienced Staff Involvement', '0000'),
    ('Hot Work (Welding, Cutting, Burning)', '0000'),
    ('Use of Hydraulic/Pneumatic Systems', '0000'),
    ('Use of Compressed Gases/Cryogenics', '0000'),
    ('Use of Flammable/Oxidising/Explosive Substances', '0000'),
    ('Use of Toxic/Corrosive Substances', '0000'),
    ('Use of Radionuclides*1', '0000'),
    ('Work in Extremes of Temperature', '0000'),
    ('Production of Hazardous Waste*1', '0000'),
    ('Handling of Glass', '0000'),
    ('Use of Moving Machinery/Entanglement Risk', '0000'),
    ('Hazardous Noise Levels', '0000'),
    ('Work in Confined/Enclosed Spaces', '0000'),
    ('Work in Areas with Reduced Ventilation', '0000'),
    ('Use/Handling of Hazardous Chemicals', '0000'),
    ('Use of Specialist Protective Equipment', '0000'),
    ('Increased Security Hazards (Due to Operational Area/Equipment)', '0000'),
    ('Navigational Hazards', '0000'),
    ('Adverse Media Coverage, Legal or Statutory Breach', '0000'),
    ('Manoeuvring of heavy loads at sea', '0000'),
    ('Use of small boats', '0000'),
    ('Use of Divers', '0000'),
    ('Use of Hand Held Power Tools', '0000'),
    ('Use of Hand/Foot Operated Machinery', '0000'),
    ('Use of Hand Tools', '0000'),
    ('Repetitive Movements', '0000'),
    ('Slips/Trips/Falls', '0000'),
    ('Collapse of Structure', '0000'),
    ('Sharp Objects (Knives, Razors, Medical Sharps etc.)', '0000'),
    ('Violence/Harassment/Bullying', '0000'),
    ('Lone Working/Out of Hours', '0000'),
    ('Carrying Equipment up to the Gangway', '0000'),
    ('Environmental Pollution', '0000'),
    ('Fumes', '0000'),
    ('Low Light Levels', '0000'),
    ('Anticipated Extended Periods of Adverse Weather', '0000'),
    ('Compressed Air', '0000'),
    ('Excessive Vibration', '0000'),
    ('Stacking/Storing', '0000'),
    ('Work on Ship''s Electrical Supply', '0000'),
    ('Towing Operations', '0000'),
    ('Anticipated Working of Excessive Hours or Other Fatigue Causing Operat...', '0000'),
    ('Potential Breach of SMS Procedures', '0000'),
    ('Unfamiliar/New Activity or New Machinery or New Vessel Type Operation', '0000');


INSERT INTO
    main.parameter_type (name, created_by, is_required_for_risk_rating)
VALUES
    ('People', '0000', TRUE),
    ('Environment', '0000',TRUE),
    ('Asset', '0000', TRUE),
    ('Reputation', '0000', TRUE),
    ('Higher Risk Groups', '0000', FALSE);

-- Sample data for risk_parameters
INSERT INTO main.parameter (parameter_type_id, name, created_by)
VALUES
    -- PEOPLE (1)
    (1, 'Employees/Crew', '0000'),
    (1, 'Repair Workshop & Riding Crew', '0000'),
    (1, 'Visitors', '0000'),
    (1, 'Authorities', '0000'),
    (1, 'Vendors', '0000'),
    (1, 'Plots/Canal Crew', '0000'),

    -- ENVIRONMENT (2)
    (2, 'Temperature', '0000'),
    (2, 'Noise', '0000'),
    (2, 'Water Disposal', '0000'),
    (2, 'Atmosphere', '0000'),
    (2, 'Humidity', '0000'),
    (2, 'Vibration', '0000'),
    (2, 'Emissions', '0000'),
    (2, 'Vibrations', '0000'), -- included as-is from JSON, though it’s likely a duplicate of 'Vibration'
    (2, 'Lighting', '0000'),
    (2, 'Adverse Weather', '0000'),
    (2, 'Marine Pollution', '0000'),

    -- ASSET (3)
    (3, 'Loss/Damage to Vessel', '0000'),
    (3, 'Cargo Claims', '0000'),
    (3, 'Fine/Penalty/Financial Liability', '0000'),
    (3, 'Structural Damage', '0000'),
    (3, 'Piracy/Theft/Hijacking', '0000'),
    (3, 'Machinery/Equipment Damage', '0000'),
    (3, 'LOH/Off-Hire/Delays', '0000'),

    -- REPUTATION (4)
    (4, 'Adverse Media Report', '0000'),
    (4, 'Loss of Trading Approvals', '0000'),
    (4, 'PSC Action', '0000'),
    (4, 'Legal Action', '0000'),
    (4, 'Technical Hold/Blacklisting', '0000'),

    -- HIGHER RISK GROUPS (5)
    (5, 'Young Persons', '0000'),
    (5, 'Pre-Existing Medical Conditions', '0000'),
    (5, 'Disabled', '0000'),
    (5, 'Sensitive Environment', '0000'),
    (5, 'Children', '0000');

-- Sample data for task_reliability_assessment
INSERT INTO
    main.task_reliability_assessment (name, options, created_by)
VALUES
    (
        'Is adequate ''Stand-by Unit'' or ''Redundancy'', available? If so, state:',
        '["Yes", "No", "NA"]',
        '0000'
    ),
    (
        'Are sufficient number of competent personnel available to carry out this job safely and do the ship-staff have adequate traning and expertise to undertake this job?',
        '["Yes", "No", "NA"]',
        '0000'
    ),
    (
        'Are adequate ''Maker/ Company procedures'' available for commissioning and testing of the equipment and whether the historical data has been referred to?',
        '["Yes", "No", "NA"]',
        '0000'
    ),
    (
        'Have spares/ consumables and necessary tools been checked and found adequate?',
        '["Yes", "No", "NA"]',
        '0000'
    );

INSERT INTO 
     main.approval_required (name, created_by)
VALUES
    ('Managing Directors', '0000'),
    ('Board Of Directors', '0000'),
    ('CFO', '0000'),
    ('Clients', '0000'),
    ('Authorities', '0000'),
    ('Head Of IT', '0000'),
    ('HR Manager', '0000');