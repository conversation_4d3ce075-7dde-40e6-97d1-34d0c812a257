#!/bin/bash
export ENV=${ENV:-dev}
export REGION=${REGION:-ap-southeast-1}
export ACCOUNT=${ACCOUNT:-************}
echo ${REGION}
echo ${ACCOUNT}


source ./load-env-vars.sh 


cd databases/migrations
export RISK_ASSESSMENT_DB_USER='adminuser'
export RISK_ASSESSMENT_DB_PORT='5432'
export RISK_ASSESSMENT_DB_DATABASE='risk-assessment'
export RISK_ASSESSMENT_DB_HOST='paris2-dev2-risk-assessment-rds.cnokqrjedngk.ap-southeast-1.rds.amazonaws.com'
export RISK_ASSESSMENT_DB_PASSWORD='NnvwQi5xSIin3b0D2^h40MKdcLr^yXW6m66ifNWPkJDmwAz#eiDwbf^bNZo#sjKU'

npm install
 
npm run db:migrate
