{"openapi": "3.0.3", "info": {"title": "Risk Assessment API", "version": "1.0.0"}, "servers": [{"url": "http://localhost:3000/stage", "variables": {}}], "paths": {"/categories": {"get": {"tags": ["Category"], "summary": "Retrieve a list of categories", "description": "Returns a filtered list of categories. Optional `search` parameter is used to filter results.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Text to search categories by name or description", "example": "vessel"}], "responses": {"200": {"description": "A list of matching categories", "content": {"application/json": {"schema": {"type": "object", "properties": {"categories": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Vessel Type"}, "description": {"type": "string", "example": "List of vessel types used in risk assessment"}}}}}}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/hazards": {"get": {"tags": ["Hazards"], "summary": "Retrieve a list of hazards", "description": "Returns a filtered list of hazards. Optional `search` parameter is used to filter results.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Text to search hazards by name or description", "example": null}], "responses": {"200": {"description": "A list of matching hazards", "content": {"application/json": {"schema": {"type": "object", "properties": {"hazards": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Hazard Type"}, "description": {"type": "string", "example": "List of Hzards used in risk assessment"}}}}}}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/parameter-types": {"get": {"tags": ["Parameter Types"], "summary": "Retrieve a list of parameter types", "description": "Returns a filtered list of parameter types. Optional `search` parameter is used to filter results.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Text to search parameter types by name or description", "example": "type1"}, {"in": "query", "name": "is_required_for_risk_rating", "schema": {"type": "boolean"}, "description": "Filter to show only required parameter types", "example": true}], "responses": {"200": {"description": "A list of matching parameter types", "content": {"application/json": {"schema": {"type": "object", "properties": {"parameterTypes": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Type 1"}, "description": {"type": "string", "example": "Description of Type 1"}}}}}}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/parameters": {"get": {"tags": ["Parameters"], "summary": "Retrieve a list of parameters", "description": "Returns a filtered list of parameters. Optional `search` parameter is used to filter results.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Text to search parameters by name or description", "example": "parameter1"}], "responses": {"200": {"description": "A list of matching parameters", "content": {"application/json": {"schema": {"type": "object", "properties": {"parameters": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Parameter 1"}, "description": {"type": "string", "example": "Description of Parameter 1"}}}}}}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/risks": {"post": {"tags": ["Risk"], "summary": "Create a new risk", "description": "Creates a new risk assessment with the provided details.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Risk"}}}}, "responses": {"201": {"description": "Risk created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Risk"}}}}, "400": {"description": "Validation error"}, "500": {"description": "Internal server error"}}}, "get": {"tags": ["Risk"], "summary": "Get a list of risks", "description": "Retrieves a list of risks with optional filters.", "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Search term to filter risks"}, {"in": "query", "name": "updatedBy", "schema": {"type": "string"}, "description": "Filter by updater ID"}, {"in": "query", "name": "updatedOn", "schema": {"type": "array", "items": {"type": "string", "format": "date"}}, "style": "form", "explode": true, "description": "Filter by update date range"}, {"in": "query", "name": "category", "schema": {"type": "integer"}, "description": "Filter by category ID"}, {"in": "query", "name": "hazardCategories", "schema": {"type": "array", "items": {"type": "integer"}}, "style": "form", "explode": true, "description": "Filter by hazard category IDs"}], "responses": {"200": {"description": "List of risks retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"result": {"type": "array", "items": {"$ref": "#/components/schemas/Risk"}}}}}}}, "500": {"description": "Internal server error"}}}}, "/risks/{id}": {"patch": {"tags": ["Risk"], "summary": "Update an existing risk", "description": "Updates an existing risk assessment with the provided details.", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the risk to update"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Risk"}}}}, "responses": {"200": {"description": "Risk updated successfully"}, "400": {"description": "Validation error"}, "404": {"description": "Risk not found"}, "500": {"description": "Internal server error"}}}, "get": {"tags": ["Risk"], "summary": "Get a risk by ID", "description": "Retrieves a risk assessment by its ID.", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the risk to retrieve"}], "responses": {"200": {"description": "Risk retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Risk"}}}}, "404": {"description": "Risk not found"}, "500": {"description": "Internal server error"}}}, "delete": {"tags": ["Risk"], "summary": "Delete a risk", "description": "Deletes a risk assessment by its ID.", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the risk to delete"}], "responses": {"200": {"description": "Risk deleted successfully"}, "404": {"description": "Risk not found"}, "500": {"description": "Internal server error"}}}}, "/task-reliability-assessment": {"get": {"tags": ["Task Reliability Assessments"], "summary": "Retrieve a list of task reliability assessments", "description": "Returns a filtered list of task reliability assessments. Optional `search` parameter is used to filter results.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Text to search task reliability assessments by name or description", "example": "assessment1"}], "responses": {"200": {"description": "A list of matching task reliability assessments", "content": {"application/json": {"schema": {"type": "object", "properties": {"taskReliabilityAssessments": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Assessment 1"}, "description": {"type": "string", "example": "Description of Assessment 1"}}}}}}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/templates": {"post": {"tags": ["Template"], "summary": "Create a new template", "description": "Creates a new template with the provided details.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"task_requiring_ra": {"type": "string", "description": "Task requiring risk assessment"}, "task_duration": {"type": "integer", "description": "Duration of the task"}, "task_duration_unit": {"type": "string", "description": "Unit of task duration"}, "template_category": {"type": "object", "properties": {"category_id": {"type": "array", "items": {"type": "integer"}}}, "description": "Categories associated with the template"}, "template_hazard": {"type": "object", "properties": {"hazard_id": {"type": "array", "items": {"type": "integer"}}, "is_other": {"type": "boolean"}, "value": {"type": "string"}}, "description": "Hazards associated with the template"}, "parameters": {"type": "array", "items": {"type": "object", "properties": {"parameter_id": {"type": "array", "items": {"type": "integer"}}, "parameter_type_id": {"type": "integer"}, "is_other": {"type": "boolean"}, "value": {"type": "string"}}}, "description": "Parameters associated with the template"}}}}}}, "responses": {"201": {"description": "Template created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}, "400": {"description": "Validation error"}, "500": {"description": "Internal server error"}}}, "get": {"tags": ["Template"], "summary": "Get a list of templates", "description": "Retrieves a list of templates with optional filters.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Search term to filter templates"}, {"in": "query", "name": "sort_by", "schema": {"type": "string"}, "description": "Field to sort by"}, {"in": "query", "name": "sort_order", "schema": {"type": "string", "enum": ["ASC", "DESC"]}, "description": "Sort order"}, {"in": "query", "name": "created_by", "schema": {"type": "array", "items": {"type": "string"}}, "style": "form", "explode": true, "description": "Filter by creator IDs"}, {"in": "query", "name": "created_by[start_date]", "schema": {"type": "string", "format": "date"}, "description": "Filter by start date of creation"}, {"in": "query", "name": "created_by[end_date]", "schema": {"type": "string", "format": "date"}, "description": "Filter by end date of creation"}, {"in": "query", "name": "ra_categories", "schema": {"type": "array", "items": {"type": "integer"}}, "style": "form", "explode": true, "description": "Filter by risk assessment category IDs"}, {"in": "query", "name": "hazard_categories", "schema": {"type": "array", "items": {"type": "integer"}}, "style": "form", "explode": true, "description": "Filter by hazard category IDs"}, {"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of templates retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}, "pagination": {"type": "object", "properties": {"totalItems": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}}}}}}}}, "500": {"description": "Internal server error"}}}}, "/templates/{id}": {"patch": {"tags": ["Template"], "summary": "Update an existing template", "description": "Updates an existing template with the provided details.", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the template to update"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}, "responses": {"200": {"description": "Template updated successfully"}, "400": {"description": "Validation error"}, "404": {"description": "Temp<PERSON> not found"}, "500": {"description": "Internal server error"}}}, "get": {"tags": ["Template"], "summary": "Get a template by ID", "description": "Retrieves a template by its ID.", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the template to retrieve"}], "responses": {"200": {"description": "Template retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}, "404": {"description": "Temp<PERSON> not found"}, "500": {"description": "Internal server error"}}}, "delete": {"tags": ["Template"], "summary": "Delete a template", "description": "Deletes a template by its ID.", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the template to delete"}], "responses": {"200": {"description": "Template deleted successfully"}, "404": {"description": "Temp<PERSON> not found"}, "500": {"description": "Internal server error"}}}}, "/templates/{id}/inactive": {"patch": {"tags": ["Template"], "summary": "Mark a template and related tables as inactive", "description": "Updates the status of a template and its related tables to inactive.", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the template to mark as inactive"}], "responses": {"200": {"description": "Template marked as inactive successfully"}, "404": {"description": "Temp<PERSON> not found"}, "500": {"description": "Internal server error"}}}}, "/templates/users": {"get": {"tags": ["Template"], "summary": "Get all unique users who created templates", "description": "Retrieves all unique user IDs from template creators and their details", "responses": {"200": {"description": "List of unique users retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"type": "object", "properties": {"userId": {"type": "string"}, "email": {"type": "string"}}}}}}}}}, "500": {"description": "Internal server error"}}}}, "/templates/top": {"get": {"tags": ["Template"], "summary": "Get top templates by risk count", "description": "Retrieves the top templates based on the count of associated risks.", "parameters": [{"in": "query", "name": "maxCount", "schema": {"type": "integer"}, "required": true, "description": "Maximum number of results to return"}], "responses": {"200": {"description": "List of top templates", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "template_count": {"type": "integer"}}}}}}}, "400": {"description": "Validation error"}, "500": {"description": "Internal server error"}}}}}, "components": {"schemas": {}, "securitySchemes": {"keycloak": {"type": "oauth2", "description": "keycloak oauth", "flows": {"implicit": {"authorizationUrl": "http://localhost:3000/stage", "scopes": {}}}}}}, "security": [{"keycloak": []}], "tags": []}