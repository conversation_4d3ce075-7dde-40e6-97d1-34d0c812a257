service: paris2-api-risk-assessment

frameworkVersion: '4'
useDotenv: true

plugins:
  - serverless-offline
  
custom:
  region: ${opt:region, 'ap-southeast-1'}
  accountId: ${param:account, '000'}
  config: ${file(paris2-configuration.json)}
  cors:
    origin:
      - '*'
    headers:
      - Content-Type
      - Content-Encoding
      - Authorization
      - Access-Control-Allow-Headers
      - Access-Control-Allow-Origin
      - Access-Control-Allow-Methods
    allowCredentials: false

layers:
  dependencies:
    path: layer
    name: ${self:service}-dependencies-${self:provider.stage}
    description: Heavy dependencies layer
    compatibleRuntimes:
      - nodejs22.x
    retain: false


provider:
  name: aws
  runtime: nodejs22.x
  timeout: 30
  stage: ${opt:stage, 'stage'}
  region: ${opt:region, 'ap-southeast-1'}
  endpointType: ${self:custom.config.api.endpoint_type}
  vpc: ${self:custom.config.network.lambda_vpc_config}
  apiGateway:
    binaryMediaTypes:
      - "*/*"
  deploymentBucket:
    name: paris2-deploy-${self:provider.stage} 
  versionFunctions: false
  logRetentionInDays: 120
  logs:
    restApi:
      level: ERROR
      fullExecutionData: false
      accessLogging: false
      executionLogging: true 
  layers:
    - { Ref: DependenciesLambdaLayer }
  environment:
    NODE_ENV: ${self:provider.stage}
    PG_SSL_ENABLED: ${env:PG_SSL_ENABLED, 'false'}
    JWT_PUBLIC_KEY: ${self:custom.config.auth.jwt_public_key}
    DB_URI: ${env:PARIS2_RISK_ASSESMENT_DB_CONNECTION}
    OPEN_ID_USERNAME: ${ssm:/paris2-new-building-open-id-username/${self:provider.stage}}
    OPEN_ID_PASSWORD: ${ssm:/paris2-new-building-open-id-password/${self:provider.stage}}
    OPEN_ID_CLIENT_SECRET: ${ssm:/paris2-new-building-open-id-client-secret/${self:provider.stage}}
    OPEN_ID_GRANT_TYPE: "password"
    OPEN_ID_CLIENT_ID: ${ssm:/paris2-new-building-open-id-client-id/${self:provider.stage}}
    PARIS2_AUTH_BASE_URL: "${self:custom.config.api.base_urls.auth}"
    PARIS2_BASE_URL: "${self:custom.config.api.base_urls.paris_api}"
  tags:
    Application ID: PARIS2
    Environment: ${self:provider.stage}
    BUSINESS UNIT: Risk-Assessment

resources:
  Resources:
    GatewayResponseDefault4XX:
      Type: "AWS::ApiGateway::GatewayResponse"
      Properties:
        ResponseParameters:
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        ResponseType: DEFAULT_4XX
        RestApiId:
          Ref: ApiGatewayRestApi
    RiskAssessmentApiMapping:
      Type: "AWS::ApiGatewayV2::ApiMapping"
      DependsOn: "ApiGatewayDeployment${sls:instanceId}"
      Properties:
        ApiId:
          Ref: ApiGatewayRestApi
        ApiMappingKey: risk-assessment
        DomainName: "${self:custom.config.api.domain_name}"
        Stage: "${self:provider.stage}"
          
functions:
  auth:
    handler: src/handlers/auth.authenticate

  categories:
    handler: src/handlers/category.main
    events:
      - http:
          method: GET
          path: /categories
          cors: '${self:custom.cors}'
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  hazards:
    handler: src/handlers/hazard.main
    events:
      - http:
          method: GET
          path: /hazards
          cors: '${self:custom.cors}'
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  parameters:
    handler: src/handlers/parameter.main
    events:
      - http:
          method: GET
          path: /parameters
          cors: '${self:custom.cors}'
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  parameterTypes:
    handler: src/handlers/parameter-type.main
    events:
      - http:
          method: GET
          path: /parameter-types
          cors: '${self:custom.cors}'
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  taskReliabilityAssessment:
    handler: src/handlers/task-reliability-assessment.main
    events:
      - http:
          method: GET
          path: /task-reliability-assessments
          cors: '${self:custom.cors}'
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  createTemplate:
    handler: src/handlers/template.createTemplateHandler
    events:
      - http:
          path: /templates
          method: post
          cors: '${self:custom.cors}'
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization
  
  patchTemplate:
    handler: src/handlers/template.patchTemplateHandler
    events:
      - http:
          path: /templates/{id}
          method: patch
          cors: '${self:custom.cors}'
          request:
            parameters:
              paths:
                id: true
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization
            
  getTemplateById:
    handler: src/handlers/template.getTemplateByIdHandler
    events:
      - http:
          path: /templates/{id}
          method: get
          cors: '${self:custom.cors}'
          request:
            parameters:
              paths:
                id: true
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  getTemplateList:
    handler: src/handlers/template.getTemplateListHandler
    events:
      - http:
          path: /templates
          method: get
          cors: '${self:custom.cors}'
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  getTemplateUsers:
    handler: src/handlers/template.getTemplateUsersHandler
    events:
      - http:
          path: /templates/users
          method: get
          cors: '${self:custom.cors}'
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  deleteTemplate:
    handler: src/handlers/template.deleteTemplateHandler
    events:
      - http:
          path: /templates/{id}
          method: delete
          cors: '${self:custom.cors}'
          request:
            parameters:
              paths:
                id: true
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  # Risk Assessment Routes
  createRisk:
    handler: src/handlers/risk.createRiskHandler
    events:
      - http:
          path: /risks
          method: post
          cors: '${self:custom.cors}'
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  patchRisk:
    handler: src/handlers/risk.patchRiskHandler
    events:
      - http:
          path: /risks/{id}
          method: patch
          cors: '${self:custom.cors}'
          request:
            parameters:
              paths:
                id: true
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  getRiskById:
    handler: src/handlers/risk.getRiskByIdHandler
    events:
      - http:
          path: /risks/{id}
          method: get
          cors: '${self:custom.cors}'
          request:
            parameters:
              paths:
                id: true
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  getRiskList:
    handler: src/handlers/risk.getRiskListHandler
    events:
      - http:
          path: /risks
          method: get
          cors: '${self:custom.cors}'
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  deleteRisk:
    handler: src/handlers/risk.deleteRiskHandler
    events:
      - http:
          path: /risks/{id}
          method: delete
          cors: '${self:custom.cors}'
          request:
            parameters:
              paths:
                id: true
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  getTopTemplates:
    handler: src/handlers/template.getTopTemplatesHandler
    events:
      - http:
          path: /templates/top
          method: get
          cors: '${self:custom.cors}'
          request:
            parameters:
              querystrings:
                maxCount: false
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  markTemplateInactive:
    handler: src/handlers/template.markTemplateInactiveHandler
    events:
      - http:
          path: /templates/{id}/inactive
          method: patch
          cors: '${self:custom.cors}'
          request:
            parameters:
              paths:
                id: true
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization
 #PARIS2 API Routes
  vesselOwnerships:
    handler: src/handlers/paris2.getVesselOwnerships
    events:
      - http:
          path: /vessel-ownership
          method: get
          cors: '${self:custom.cors}'
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization 
  getCrewList:
    handler: src/handlers/paris2.getCrewList
    events:
      - http:
          path: /crew-list
          method: get
          cors: '${self:custom.cors}'
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization
  getOfficeslist:
    handler: src/handlers/paris2.getReportingOffices
    events:
      - http:
          path: /reporting-office
          method: get
          cors: '${self:custom.cors}'
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization 

  updateRiskTeamMembers:
    handler: src/handlers/risk-team-member.updateRiskTeamMembersHandler
    events:
      - http:
          path: /risks/{id}/team-members
          method: put
          cors: '${self:custom.cors}'
          request:
            parameters:
              paths:
                id: true
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization
  getRiskOptionsByKey:
    handler: src/handlers/risk.getRiskOptionsByKeyHandler
    events:
      - http:
          path: /risks/options/{key}
          method: get
          cors: '${self:custom.cors}'
          request:
            parameters:
              paths:
                key: true
          authorizer:
            name: auth
            type: request
            identitySource: method.request.header.Authorization

  getRiskEntityOptions:
    handler: src/handlers/risk.getRiskEntityOptionsHandler
    events:
     - http:
         path: /risks/entity-options
         method: get
         cors: '${self:custom.cors}'
         request:
           parameters:
             querystrings:
              type: true
         authorizer:
           name: auth
           type: request
           identitySource: method.request.header.Authorization

package:
  individually: false
  excludeDevDependencies: true
  patterns:
    - "!node_modules/**"
    - "!**/*.test.js"
    - "!**/*.spec.js"
    - "!tests/**"
    - "!coverage/**"
    - "!.git/**"
    - "!*.md"
    - "!README*"
    - "!LICENSE*"
    - "!Jenkinsfile"
    - "!webpack.config.js"
    - "!babel.config.js"
    - "!.eslintrc*"
    - "!.prettierrc*"
    - "!tsconfig.json"
    - "!package*.json"
    - "!deploy.sh"
    - "!load-env-vars.sh"
    - "src/**"
