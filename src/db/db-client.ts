import 'pg';
import sequelize, {R<PERSON><PERSON>_DB} from './sequelize';
import associateModels from '../models/association';
import {logger} from '../utils/logger';
import {QueryTypes} from 'sequelize/types';

// Export sequelize instance for backward compatibility
export const sq = sequelize;
export {RISK_DB};

// Connection state tracking
let initialized = false;
let isClosing = false;

// For Lambda, we should reuse connections by default to avoid the
// "connection manager was closed" error on subsequent requests
// Only disable if explicitly set to 'false'
const shouldReuseConnections = process.env.LAMBDA_REUSE_CONNECTIONS !== 'false';

export const initDb = async () => {
  if (initialized || isClosing) return;

  try {
    await sequelize.authenticate();
    associateModels();

    // Don't sync models on every init in production
    if (process.env.NODE_ENV !== 'production') {
      await sequelize.sync();
    }
    initialized = true;

    try {
      const connectionStats = await sequelize.query(
        `
          SELECT
            count(*) as total_connections,
            state,
            application_name
          FROM pg_stat_activity
          WHERE application_name IS NOT NULL
          GROUP BY state, application_name
          ORDER BY total_connections DESC;
        `,
        {type: QueryTypes.SELECT},
      );

      logger.info({
        group: 'database',
        name: 'connection_stats',
        message: `Current database connections: ${JSON.stringify(connectionStats)}`,
      });
    } catch (statsErr) {
      logger.warn({
        group: 'database',
        name: 'connection_stats_error',
        message: `Could not fetch connection statistics: ${statsErr instanceof Error ? statsErr.message : 'Unknown error'}`,
      });
    }

    logger.info({
      group: 'database',
      name: 'connection',
      message: 'Database connection initialized successfully',
    });
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown database error';

    // Check if this is the "connection manager was closed" error
    if (errorMessage.includes('connection manager was closed')) {
      logger.warn({
        group: 'database',
        name: 'connection_manager_closed',
        message: 'Connection manager was closed, recreating Sequelize instance',
      });

      // Reset state and try to recreate
      initialized = false;
      isClosing = false;

      // Import a fresh sequelize instance
      const {sequelize: newSequelize} = await import('./sequelize');
      Object.setPrototypeOf(sequelize, Object.getPrototypeOf(newSequelize));
      Object.assign(sequelize, newSequelize);

      // Try again with the fresh instance
      try {
        await sequelize.authenticate();
        associateModels();
        if (process.env.NODE_ENV !== 'production') {
          await sequelize.sync();
        }
        initialized = true;

        logger.info({
          group: 'database',
          name: 'connection',
          message: 'Database connection recreated successfully',
        });
        return;
      } catch (retryErr) {
        logger.error({
          group: 'database',
          name: 'connection_recreate_error',
          message: retryErr instanceof Error ? retryErr.message : 'Failed to recreate connection',
        });
        throw retryErr;
      }
    }

    logger.error({
      group: 'database',
      name: 'connection_error',
      message: errorMessage,
    });
    throw err;
  }
};

export const closeDb = async () => {
  if (!initialized || isClosing) return;

  isClosing = true;
  try {
    await sequelize.close();
    logger.info({
      group: 'database',
      name: 'connection',
      message: 'Database connection closed successfully',
    });
  } catch (err) {
    logger.error({
      group: 'database',
      name: 'connection_error',
      message: err instanceof Error ? err.message : 'Unknown database error',
    });
    // Don't re-throw in cleanup to avoid masking original errors
  } finally {
    initialized = false;
    isClosing = false;
  }
};

// Emergency function to force close all connections
export const forceCloseDb = async () => {
  logger.warn({
    group: 'database',
    name: 'force_close',
    message: 'Force closing database connections',
  });

  try {
    await sequelize.close();
    logger.info({
      group: 'database',
      name: 'force_close',
      message: 'Force close completed successfully',
    });
  } catch (err) {
    logger.error({
      group: 'database',
      name: 'force_close_error',
      message:
        err instanceof Error ? err.message : 'Unknown error during force close',
    });
  } finally {
    initialized = false;
    isClosing = false;
  }
};
