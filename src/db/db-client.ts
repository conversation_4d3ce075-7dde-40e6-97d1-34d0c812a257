import 'pg';
import sequelize, {RISK_DB} from './sequelize';
import associateModels from '../models/association';
import {logger} from '../utils/logger';

// Export sequelize instance for backward compatibility
export const sq = sequelize;
export {RISK_DB};

// Connection state tracking
let initialized = false;
let isClosing = false;

// For Lambda, we might want to reuse connections across invocations
// This helps reduce cold start times and connection overhead
const shouldReuseConnections = true;

export const initDb = async () => {
  if (initialized || isClosing) return;

  try {
    await sequelize.authenticate();
    associateModels();
    
    // Don't sync models on every init in production
    if (process.env.NODE_ENV !== 'production') {
      await sequelize.sync();
    }
    initialized = true;
    
    logger.info({
      group: 'database',
      name: 'connection',
      message: 'Database connection initialized successfully',
    });
  } catch (err) {
    logger.error({
      group: 'database',
      name: 'connection_error',
      message: err instanceof Error ? err.message : 'Unknown database error',
    });
    throw err;
  }
};

export const closeDb = async () => {
  if (!initialized || isClosing) return;

  // If connection reuse is enabled, don't close connections
  // This helps with Lambda performance but requires careful monitoring
  if (shouldReuseConnections) {
    logger.debug({
      group: 'database',
      name: 'connection',
      message: 'Keeping database connection open for reuse',
    });
    return;
  }

  isClosing = true;
  try {
    await sequelize.close();
    logger.info({
      group: 'database',
      name: 'connection',
      message: 'Database connection closed successfully',
    });
  } catch (err) {
    logger.error({
      group: 'database',
      name: 'connection_error',
      message: err instanceof Error ? err.message : 'Unknown database error',
    });
    // Don't re-throw in cleanup to avoid masking original errors
  } finally {
    initialized = false;
    isClosing = false;
  }
};
