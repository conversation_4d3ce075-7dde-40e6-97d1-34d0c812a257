import 'pg';
import sequelize, {R<PERSON><PERSON>_DB} from './sequelize';
import associateModels from '../models/association';
import {logger} from '../utils/logger';
import {QueryTypes} from 'sequelize/types';

// Export sequelize instance for backward compatibility
export const sq = sequelize;
export {RISK_DB};

// Connection state tracking
let initialized = false;
let isClosing = false;

// For Lambda, we don't close Sequelize connections manually
// AWS will clean them up when the container is destroyed

export const initDb = async () => {
  if (initialized || isClosing) {
    logger.debug({
      group: 'database',
      name: 'connection',
      message: `Skipping initDb - already initialized: ${initialized}, closing: ${isClosing}`,
    });
    return;
  }

  try {
    await sequelize.authenticate();
    associateModels();

    // Don't sync models on every init in production
    if (process.env.NODE_ENV !== 'production') {
      await sequelize.sync();
    }
    initialized = true;

    try {
      const connectionStats = await sequelize.query(
        `
          SELECT
            count(*) as total_connections,
            state,
            application_name
          FROM pg_stat_activity
          WHERE application_name IS NOT NULL
          GROUP BY state, application_name
          ORDER BY total_connections DESC;
        `,
        {type: QueryTypes.SELECT},
      );

      logger.info({
        group: 'database',
        name: 'connection_stats',
        message: `Current database connections: ${JSON.stringify(connectionStats)}`,
      });
    } catch (statsErr) {
      logger.warn({
        group: 'database',
        name: 'connection_stats_error',
        message: `Could not fetch connection statistics: ${statsErr instanceof Error ? statsErr.message : 'Unknown error'}`,
      });
    }

    logger.info({
      group: 'database',
      name: 'connection',
      message: 'Database connection initialized successfully',
    });
  } catch (err) {
    logger.error({
      group: 'database',
      name: 'connection_error',
      message: err instanceof Error ? err.message : 'Unknown database error',
    });
    throw err;
  }
};

export const closeDb = async () => {
  // For Lambda functions, we don't close Sequelize connections
  // This prevents "connection manager was closed" errors on subsequent requests
  // AWS will clean up connections when the Lambda container is destroyed
  logger.debug({
    group: 'database',
    name: 'connection',
    message: 'Keeping database connection open (Lambda best practice)',
  });
};


