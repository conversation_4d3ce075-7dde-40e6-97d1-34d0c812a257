import 'pg';
import sequelize, {RISK_DB} from './sequelize';
import associateModels from '../models/association';
import {logger} from '../utils/logger';

// Export sequelize instance for backward compatibility
export const sq = sequelize;
export {RISK_DB};

// Connection state tracking
let initialized = false;
let isClosing = false;

export const initDb = async () => {
  if (initialized || isClosing) return;

  try {
    await sequelize.authenticate();
    associateModels();
    
    // Don't sync models on every init in production
    if (process.env.NODE_ENV !== 'production') {
      await sequelize.sync();
    }
    initialized = true;
    
    logger.info({
      group: 'database',
      name: 'connection',
      message: 'Database connection initialized successfully',
    });
  } catch (err) {
    logger.error({
      group: 'database',
      name: 'connection_error',
      message: err instanceof Error ? err.message : 'Unknown database error',
    });
    throw err;
  }
};

export const closeDb = async () => {
  // if (!initialized || isClosing) return;
  
  // isClosing = true;
  // try {
  //   await sequelize.close();
  //   logger.info({
  //     group: 'database',
  //     name: 'connection',
  //     message: 'Database connection closed successfully',
  //   });
  // } catch (err) {
  //   logger.error({
  //     group: 'database',
  //     name: 'connection_error',
  //     message: err instanceof Error ? err.message : 'Unknown database error',
  //   });
  //   throw err; // Re-throw to ensure Lambda handler knows about the failure
  // } finally {
  //   initialized = false;
  //   isClosing = false;
  // }
};
