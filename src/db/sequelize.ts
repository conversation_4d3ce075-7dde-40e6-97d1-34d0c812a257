import {Sequelize} from 'sequelize';
import dotenv from 'dotenv';
dotenv.config();

const dbUri = process.env.DB_URI ?? '';
export const RISK_DB = dbUri?.split('/').pop() ?? 'risk-assessment';

const ssl =
  process.env.PG_SSL_ENABLED === 'true' || process.env.PG_SSL_ENABLED === 'TRUE'
    ? {require: true, rejectUnauthorized: false}
    : undefined;

export const sequelize = new Sequelize(dbUri, {
  dialect: 'postgres',
  dialectOptions: {
    ssl: ssl,
  },
  pool: {
    max: 5,      // Reduced to prevent connection exhaustion
    min: 0,      // Keep at 0 for Lambda cold starts
    acquire: 10000,  // 10 seconds max to get connection (less than Lambda timeout)
    idle: 30000,     // 30 seconds before marking connection as idle
    evict: 60000,    // 60 seconds before removing idle connections
  },
  logging: false,
});

export default sequelize;
