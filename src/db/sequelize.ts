import {Sequelize} from 'sequelize';
import dotenv from 'dotenv';
dotenv.config();

const dbUri = process.env.DB_URI ?? '';
export const RISK_DB = dbUri?.split('/').pop() ?? 'risk-assessment';

const ssl =
  process.env.PG_SSL_ENABLED === 'true' || process.env.PG_SSL_ENABLED === 'TRUE'
    ? {require: true, rejectUnauthorized: false}
    : undefined;

export const sequelize = new Sequelize(dbUri, {
  dialect: 'postgres',
  dialectOptions: {
    ssl: ssl,
  },
  pool: {
    max: 3,      // Conservative limit per Lambda container
    min: 0,      // No minimum connections for Lambda
    acquire: 10000,  // 10 seconds max to get connection
    idle: 30000,     // 30 seconds idle timeout
  },
  logging: false,
});

export default sequelize;
