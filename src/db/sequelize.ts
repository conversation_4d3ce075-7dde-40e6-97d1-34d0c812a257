import {Sequelize} from 'sequelize';
import dotenv from 'dotenv';
dotenv.config();

const dbUri = process.env.DB_URI ?? '';
export const RISK_DB = dbUri?.split('/').pop() ?? 'risk-assessment';

const ssl =
  process.env.PG_SSL_ENABLED === 'true' || process.env.PG_SSL_ENABLED === 'TRUE'
    ? {require: true, rejectUnauthorized: false}
    : undefined;

export const sequelize = new Sequelize(dbUri, {
  dialect: 'postgres',
  dialectOptions: {
    ssl: ssl,
  },
  pool: {
    max: 10,  // Reduced from 500 - more appropriate for Lambda
    min: 0,  // Keep at 0 for Lambda cold starts
    acquire: 30000,
    idle: 5000,
  },
  logging: false,
});

export default sequelize;
