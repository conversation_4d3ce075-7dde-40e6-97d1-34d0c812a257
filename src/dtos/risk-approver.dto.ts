import Joi from 'joi';
import {RiskAssessmentRALevel} from '../enums/ra-level.enum';

export interface RiskApproverInput {
  risk_id: number;
  ra_level: number;
  keycloak_id: number[];
}

export const validateRiskApprover = (input: RiskApproverInput) => {
  const schema = Joi.object({
    risk_id: Joi.number().required(),
    ra_level: Joi.number().integer().min(1).max(3).required(),
    keycloak_id: Joi.array()
      .items(Joi.number().integer())
      .custom((value, helpers) => {
        const {ra_level} = helpers.state.ancestors[0];

        if (ra_level === 1 && value.length !== 1) {
          return helpers.error('array.length', {
            message:
              'Invalid number of approvers for ra_level 1. Only 1 approver is required.',
          });
        }

        if ((ra_level === 2 || ra_level === 3) && value.length !== 3) {
          return helpers.error('array.length', {
            message: `Invalid number of approvers for ra_level ${ra_level}. 3 approvers are required.`,
          });
        }

        return value;
      })
      .required(),
  });

  const {error, value} = schema.validate(input, {abortEarly: false});

  if (error) {
    throw new Error(error.details.map(detail => detail.message).join(', '));
  }

  return value;
};

export interface UpdateRiskApproverStatusInput {
  risk_id: number;
  status: number;
  message?: string;
}

export const validateUpdateRiskApproverStatus = (
  input: UpdateRiskApproverStatusInput,
) => {
  const schema = Joi.object({
    risk_id: Joi.number().required(),
    status: Joi.number().valid(1, 2, 3).required().messages({
      'any.only':
        'Status must be one of: 1 (APPROVED), 2 (REJECTED), 3 (CONDITIONALLY_APPROVED)',
    }),
    message: Joi.string()
      .when('status', {
        is: Joi.number().valid(2, 3),
        then: Joi.string().required(),
        otherwise: Joi.string().optional(),
      })
      .messages({
        'any.required':
          'Message is required when status is REJECTED or CONDITIONALLY_APPROVED',
      }),
  });

  const {error, value} = schema.validate(input, {abortEarly: false});

  if (error) {
    throw new Error(error.details.map(detail => detail.message).join(', '));
  }

  return value;
};

export interface UpdateRiskApproversInput {
  ra_level: RiskAssessmentRALevel;
  approvers: number[];
}

export const validateUpdateRiskApprovers = (input: UpdateRiskApproversInput) => {
  const schema = Joi.object({
    ra_level: Joi.number()
      .valid(...Object.values(RiskAssessmentRALevel).filter(v => typeof v === 'number'))
      .required()
      .messages({
        'any.only': 'RA level must be one of: ROUTINE, SPECIAL, or CRITICAL',
      }),
    // approvers: Joi.array()
    //   .items(Joi.number().integer().required())
    //   .min(1)
    //   .required()
    //   .messages({
    //     'array.min': 'At least one approver is required',
    //     'array.base': 'Approvers must be an array of numbers',
    //   }),
  });

  const {error, value} = schema.validate(input, {abortEarly: false});

  if (error) {
    throw new Error(error.details.map(detail => detail.message).join(', '));
  }

  return value;
};
