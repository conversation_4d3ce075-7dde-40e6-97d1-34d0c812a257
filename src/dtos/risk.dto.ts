import Joi from 'joi';
import {RiskAssessmentStatus} from '../enums';

export const getRiskListSchema = Joi.object({
  search: Joi.string()
    .trim()
    .allow('', null)
    .description('Search in task_requiring_ra field only'),
  vessel_name: Joi.string()
    .trim()
    .allow('', null)
    .description('Filter by vessel name'),
  office_name: Joi.string()
    .trim()
    .allow('', null)
    .description('Filter by office name'),
  vessel_category: Joi.string()
    .trim()
    .allow('', null)
    .description('Filter by vessel category'),
  ra_level: Joi.number()
    .integer()
    .min(1)
    .max(4)
    .allow(null)
    .description('Filter by risk assessment level (1-4)'),
  submitted_on: Joi.object({
    start_date: Joi.date()
      .required()
      .description('Start date for submission filter (created_at)'),
    end_date: Joi.date()
      .min(Joi.ref('start_date'))
      .required()
      .description('End date for submission filter (created_at)'),
  }).allow(null),
  assessment_date: Joi.object({
    start_date: Joi.date()
      .required()
      .description(
        'Start date for risk assessment filter (date_risk_assessment)',
      ),
    end_date: Joi.date()
      .min(Joi.ref('start_date'))
      .required()
      .description(
        'End date for risk assessment filter (date_risk_assessment)',
      ),
  }).allow(null),
  approval_date: Joi.object({
    start_date: Joi.date()
      .required()
      .description('Start date for approval filter (publish_on)'),
    end_date: Joi.date()
      .min(Joi.ref('start_date'))
      .required()
      .description('End date for approval filter (publish_on)'),
  }).allow(null),
  'approval_status[]': Joi.array()
    .items(
      Joi.number().valid(
        RiskAssessmentStatus.APPROVED,
        RiskAssessmentStatus.PENDING,
        RiskAssessmentStatus.REJECTED,
      ),
    )
    .allow(null)
    .description('Array of approval status values'),
  status: Joi.number()
    .valid(RiskAssessmentStatus.DRAFT, RiskAssessmentStatus.PUBLISHED)
    .default(RiskAssessmentStatus.PUBLISHED)
    .description('Risk status (1: Draft, 2: Published)'),
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .description('Page number for pagination'),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .description('Items per page'),
  sort_by: Joi.string()
    .valid('created_at', 'updated_at', 'task_requiring_ra')
    .default('created_at')
    .description('Field to sort by'),
  sort_order: Joi.string()
    .valid('ASC', 'DESC')
    .default('ASC')
    .description('Sort direction'),
}).unknown(true);

export interface RiskListQueryParams {
  search?: string;
  vessel_name?: string;
  office_name?: string;
  vessel_category?: string;
  ra_level?: number;
  submitted_on?: {
    start_date: Date;
    end_date: Date;
  };
  assessment_date?: {
    start_date: Date;
    end_date: Date;
  };
  approval_date?: {
    start_date: Date;
    end_date: Date;
  };
  'approval_status[]'?: number[];
  status?: RiskAssessmentStatus;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: string;
}

// Additional response types for type safety
export interface RiskListResponse {
  message: string;
  result: any[];
}

export interface RiskResponse {
  message: string;
  result: any;
}
