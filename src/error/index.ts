import {StatusCodes} from '../enums';

export const handleError = (error: any) => {
  console.error('Error:', error);

  if (error.name === 'SequelizeValidationError') {
    return {
      statusCode: StatusCodes.BAD_REQUEST,
      response: {
        message: error.message,
        errors: error.errors.map((err: any) => ({
          field: err.path,
          message: err.message,
        })),
      },
    };
  }

  if (error.name === 'SequelizeForeignKeyConstraintError') {
    return {
      statusCode: StatusCodes.BAD_REQUEST,
      response: {
        message: `Invalid reference provided for ${String(error.table).replace(
          /_/g,
          ' ',
        )}`,
      },
    };
  }

  return {
    statusCode: StatusCodes.INTERNAL_SERVER_ERROR,
    response: {
      message: 'Internal server error',
    },
  };
};

export const handleValidationError = (message: string) => ({
  statusCode: StatusCodes.BAD_REQUEST,
  response: {
    message,
  },
});

export const handleNotFoundError = (resource: string) => ({
  statusCode: StatusCodes.NOT_FOUND,
  response: {
    message: `${resource} not found`,
  },
});

export class CustomHttpError extends Error {
  statusCode: number;
  response: {message: string};
  constructor(statusCode: number, message: string) {
    super(message);
    this.statusCode = statusCode;
    this.response = {message};
    Object.setPrototypeOf(this, new.target.prototype);
  }
}
