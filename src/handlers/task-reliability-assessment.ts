import {handleNotFoundError} from '../error';
import {createLambda} from '../utils/lambda';
import TaskReliabilityAssessmentController from '../controller/task-reliability-assessment.controller';
import {LambdaData, LambdaResponse} from '../types/lambda';
import {RISK_DB} from '../db/db-client';

/**
 * @swagger
 * /task-reliability-assessment:
 *   get:
 *     tags:
 *       - Task Reliability Assessments
 *     summary: Retrieve a list of task reliability assessments
 *     description: Returns a filtered list of task reliability assessments. Optional `search` parameter is used to filter results.

 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Text to search task reliability assessments by name or description
 *         example: assessment1
 *     responses:
 *       200:
 *         description: A list of matching task reliability assessments
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 taskReliabilityAssessments:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: "Assessment 1"
 *                       description:
 *                         type: string
 *                         example: "Description of Assessment 1"
 *       401:
 *         description: Unauthorized (role missing or invalid token)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Unauthorized
 *       404:
 *         description: Path not found (for unsupported methods)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Path not found
 *       500:
 *         description: Internal server error
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Internal error occurred
 */
export const main = createLambda(
  async ({
    method,
    queryStringParameters,
  }: LambdaData): Promise<LambdaResponse> => {
    if (method !== 'GET') {
      return handleNotFoundError('Path not found');
    }

    const search = queryStringParameters?.search ?? '';
    return {
      statusCode: 200,
      response: await TaskReliabilityAssessmentController.list(search),
    };
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => {
      return Promise.resolve();
    },
  },
);
