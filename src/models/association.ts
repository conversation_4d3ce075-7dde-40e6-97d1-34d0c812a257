// associations.ts
import {setupParameterTypeAssociations} from './parameter-type.model';
import {setupParameterAssociations} from './parameter.model';
import {setupTemplateCategoryAssociations} from './template-category.model';
import {setupTemplateHazardAssociations} from './template-hazard.model';
import {setupTemplateJobInitialRiskRatingAssociations} from './template-job-initial-risk-rating.model';
import {setupTemplateJobResidualRiskRatingAssociations} from './template-job-residual-risk-rating.model';
import {setupTemplateJobAssociations} from './template-job.model';
import {setupTemplateKeywordAssociations} from './template-keyword.model';
import {setupTemplateParameterAssociations} from './template-parameter.model';
import {setupTemplateTaskReliabilityAssessmentAssociations} from './template-task-reliability-assessment.model';
import {setupTemplateAssociations} from './template.model';
import {setupRiskAssociations} from './risk.model';
import {setupRiskTeamMemberAssociations} from './risk-team-member.model';
import {setupRiskCategoryAssociations} from './risk-category.model';
import {setupRiskHazardAssociations} from './risk-hazard.model';
import {setupRiskParameterAssociations} from './risk-parameter.model';
import {setupRiskJobAssociations} from './risk-job.model';
import {setupRiskJobInitialRiskRatingAssociations} from './risk-job-initial-risk-rating.model';
import {setupRiskJobResidualRiskRatingAssociations} from './risk-job-residual-risk-rating.model';
import {setupRiskTaskReliabilityAssessmentAssociations} from './risk-task-reliability-assessment.model';
import {setupRiskApproverAssociations} from './risk-approver.model';

let associationsInitialized = false;
const associateModels = () => {
  if (associationsInitialized) return;
  associationsInitialized = true;
  setupParameterAssociations(); // Setup Parameter associations first
  setupParameterTypeAssociations(); // Then ParameterType associations
  setupTemplateAssociations(); // Then Template associations
  setupTemplateCategoryAssociations(); // Then Template Category associations
  setupTemplateHazardAssociations(); // Then Template Hazard associations
  setupTemplateJobAssociations(); // Then Template Job associations
  setupTemplateParameterAssociations(); // Then Template Parameter associations
  setupTemplateJobInitialRiskRatingAssociations(); // Then Template Job Initial Risk Rating associations
  setupTemplateJobResidualRiskRatingAssociations(); // Then Template Job Residual Risk Rating associations
  setupTemplateTaskReliabilityAssessmentAssociations(); // Finally Template Task Reliability Assessment associations
  setupRiskAssociations();
  setupRiskTeamMemberAssociations();
  setupRiskCategoryAssociations();
  setupRiskHazardAssociations();
  setupRiskParameterAssociations();
  setupRiskJobAssociations();
  setupRiskJobInitialRiskRatingAssociations();
  setupRiskJobResidualRiskRatingAssociations();
  setupRiskTaskReliabilityAssessmentAssociations();
  setupRiskApproverAssociations();
  setupTemplateKeywordAssociations();
};

export default associateModels;
