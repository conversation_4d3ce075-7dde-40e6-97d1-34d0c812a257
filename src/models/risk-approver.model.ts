import {DataTypes, Model} from 'sequelize';
import sequelize from '../db/sequelize';
import CommonModel from './common.model';
import Risk from './risk.model';

interface IRiskApprover {
  id?: number;
  risk_id: number;
  keycloak_id: number;
  user_name: string;
  user_email: string;
  job_title: string;
  message?: string;
  approval_status?: number;
  status: number;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

const RiskApprover = sequelize.define<Model<IRiskApprover> & IRiskApprover>(
  'risk_approver',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    risk_id: {
      type: DataTypes.INTEGER,
      field: 'risk_id',
      allowNull: false,
      references: {
        model: Risk,
        key: 'id',
      },
    },
    keycloak_id: {
      type: DataTypes.INTEGER,
      field: 'keycloak_id',
      allowNull: false,
    },
    user_name: {
      type: DataTypes.STRING,
      field: 'user_name',
      allowNull: false,
    },
    user_email: {
      type: DataTypes.STRING(100),
      field: 'user_email',
      allowNull: false,
    },
    job_title: {
      type: DataTypes.STRING,
      field: 'job_title',
      allowNull: false,
    },
    message: {
      type: DataTypes.STRING,
      field: 'message',
      allowNull: true,
    },
    approval_status: {
      type: DataTypes.INTEGER,
      field: 'approval_status',
      allowNull: false,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      defaultValue: 1,
      allowNull: false,
    },
    ...CommonModel,
  },
  {
    modelName: 'risk_approver',
    tableName: 'risk_approver',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupRiskApproverAssociations = () => {
  RiskApprover.belongsTo(Risk, {
    foreignKey: 'risk_id',
    as: 'risk',
  });
};

export default RiskApprover;
