import {RiskAssessmentRALevel} from '../enums/ra-level.enum';
import {RiskAssessmentStatus} from '../enums';

// Base interface for common audit fields
export interface IBaseAttributes {
  status: number;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

// ---- Model Attributes ---- //

export interface IRiskTeamMemberAttributes extends IBaseAttributes {
  id: number;
  risk_id: number;
  seafarer_id?: number;
  seafarer_person_id?: number;
  seafarer_hkid?: number;
  seafarer_name: string;
  seafarer_rank: string;
  seafarer_rank_id?: number;
  seafarer_rank_sort_order?: string;
}

export interface IRiskCategoryAttributes extends IBaseAttributes {
  id: number;
  risk_id: number;
  category_id: number;
  category_is_other: boolean;
}

export interface IRiskHazardAttributes extends IBaseAttributes {
  id: number;
  risk_id: number;
  hazard_id?: number;
  value?: string;
  hazard_category_is_other: boolean;
}

export interface IRiskParameterAttributes extends IBaseAttributes {
  id: number;
  risk_id: number;
  parameter_type_id: number;
  value?: string;
  parameter_id?: number;
  parameter_is_other: boolean;
}

export interface IRiskJobAttributes extends IBaseAttributes {
  id: number;
  risk_id: number;
  job_step: string;
  job_hazard: string;
  job_nature_of_risk: string;
  job_existing_control: string;
  job_additional_mitigation?: string;
  job_close_out_date?: Date;
  job_close_out_responsibility_id?: string;
}

export interface IRiskJobInitialRatingAttributes extends IBaseAttributes {
  id: number;
  risk_job_id: number;
  parameter_type_id: number;
  rating: string;
}

export interface IRiskJobResidualRatingAttributes extends IBaseAttributes {
  id: number;
  risk_job_id: number;
  parameter_type_id: number;
  rating: string;
  reason?: string;
}

export interface IRiskTaskReliabilityAssessmentAttributes
  extends IBaseAttributes {
  id: number;
  risk_id: number;
  task_reliability_assessment_id: number;
  task_reliability_assessment_answer: string;
  condition?: string;
}

export interface IRiskAttributes extends IBaseAttributes {
  id: number;
  template_id?: number;
  task_requiring_ra: string;
  assessor: number;
  vessel_ownership_id?: number;
  vessel_id?: number;
  vessel_code?: number;
  vessel_name?: string;
  vessel_category?: string;
  vessel_tech_group?: string;
  office_id?: number;
  office_name?: string;
  date_risk_assessment?: Date;
  task_duration: string;
  approval_date?: Date;
  ra_level: RiskAssessmentRALevel;
  task_alternative_consideration?: string;
  task_rejection_reason?: string;
  worst_case_scenario?: string;
  recovery_measures?: string;
  status: RiskAssessmentStatus;
  publish_on?: Date;
  draft_step?: number;
}

// ---- Request/Response Types ---- //

// Team Member
export type IRiskTeamMember = Omit<
  IRiskTeamMemberAttributes,
  'id' | 'risk_id' | keyof IBaseAttributes
>;

// Category
export interface IRiskCategory {
  is_other: boolean;
  category_id: number[];
  value?: string;
}

// Hazard
export interface IRiskHazard {
  is_other: boolean;
  hazard_id: number[];
  value?: string;
}

// Parameter
export interface IRiskParameter {
  is_other: boolean;
  parameter_type_id: number;
  parameter_id: number[];
  value?: string;
}

// Job Rating (shared for initial and residual)
export interface IRiskJobRating {
  parameter_type_id: number;
  rating: string;
  reason?: string;
}

// Task Reliability Assessment
export type IRiskTaskReliabilityAssessment = Omit<
  IRiskTaskReliabilityAssessmentAttributes,
  'id' | 'risk_id' | keyof IBaseAttributes
>;

// Job with ratings and lowering reasons
export interface IRiskJob
  extends Omit<IRiskJobAttributes, 'id' | 'risk_id' | keyof IBaseAttributes> {
  risk_job_initial_risk_rating: IRiskJobRating[];
  risk_job_residual_risk_rating: IRiskJobRating[];
}

// ---- Main Create/Update Types ---- //

export interface IRiskCreate
  extends Omit<IRiskAttributes, 'id' | keyof IBaseAttributes | 'status'> {
  template_id?: number;
  status: string;
  risk_team_member?: IRiskTeamMember[];
  risk_category?: IRiskCategory;
  risk_hazard?: IRiskHazard;
  parameters?: IRiskParameter[];
  risk_job?: IRiskJob[];
  risk_task_reliability_assessment?: IRiskTaskReliabilityAssessment[];
}

// Helper types
export type RiskAttributes = IRiskAttributes;
export type CreateRiskInput = Omit<
  RiskAttributes,
  'id' | 'created_at' | 'updated_at'
>;
export type UpdateRiskInput = Partial<CreateRiskInput>;

// Response types
export interface IRiskListResponse {
  message: string;
  result: IRiskAttributes[];
}

export interface IRiskResponse {
  message: string;
  result: IRiskAttributes;
}

export type GetRiskListPayload = {
  search?: string;
  updatedBy?: string;
  updatedOn?: [string, string];
  category?: number;
  hazardCategories?: number[];
};
