import Joi from 'joi';
import paris2Controller from '../controller/paris2-api-controller';
import {
  IRiskCreate,
  IRiskTeamMember,
  IRiskCategory,
  IRiskHazard,
  IRiskJob,
  IRiskJobRating,
  IRiskTaskReliabilityAssessment,
  IRiskParameter,
} from '../types/risk.types';

// Base validation messages
const messages = {
  'string.empty': '{{#label}} cannot be empty',
  'any.required': '{{#label}} is required',
  'array.min': '{{#label}} must contain at least {{#limit}} item',
  'array.base': '{{#label}} must be an array',
  'number.base': '{{#label}} must be a number',
  'date.base': '{{#label}} must be a valid date',
  'boolean.base': '{{#label}} must be a boolean',
};

const riskJobRatingSchema = Joi.object<IRiskJobRating>({
  parameter_type_id: Joi.number().required().messages({
    'any.required': 'Parameter type ID is required',
    'number.base': 'Parameter type ID must be a number',
  }),
  rating: Joi.string().max(2).required().messages({
    'any.required': 'Rating is required',
    'string.base': 'Rating must be a string',
  }),
  reason: Joi.string().optional().max(255).messages({
    'string.base': 'Reason must be a string',
  }),
});

const jobSchema = Joi.object<IRiskJob>({
  job_step: Joi.string().max(255).required().messages({
    'any.required': 'Job step is required',
    'string.base': 'Job step must be a string',
  }),
  job_hazard: Joi.string().max(255).required().messages({
    'any.required': 'Job hazard is required',
    'string.base': 'Job hazard must be a string',
  }),
  job_nature_of_risk: Joi.string().max(255).required().messages({
    'any.required': 'Job nature of risk is required',
    'string.base': 'Job nature of risk must be a string',
  }),
  job_existing_control: Joi.string().max(4000).required().messages({
    'any.required': 'Job existing control is required',
    'string.base': 'Job existing control must be a string',
  }),
  job_additional_mitigation: Joi.string().max(4000).required().messages({
    'any.required': 'Job additional mitigation is required',
    'string.base': 'Job additional mitigation must be a string',
  }),
  job_close_out_date: Joi.date().required().messages({
    'any.required': 'Job close out date is required',
    'date.base': 'Job close out date must be a valid date',
  }),
  job_close_out_responsibility_id: Joi.string().required().messages({
    'any.required': 'Job close out responsibility ID is required',
    'string.base': 'Job close out responsibility ID must be a string',
  }),
  risk_job_initial_risk_rating: Joi.array()
    .items(riskJobRatingSchema)
    .required(),
  risk_job_residual_risk_rating: Joi.array()
    .items(riskJobRatingSchema)
    .required(),
});

const teamMemberSchema = Joi.object<IRiskTeamMember>({
  seafarer_id: Joi.number()
    .required()
    .messages({
      ...messages,
      'any.required': 'Team member seafarer ID is required',
    }),
  seafarer_hkid: Joi.number().optional(),
  seafarer_person_id: Joi.number().optional(),
  seafarer_name: Joi.string()
    .required()
    .messages({
      ...messages,
      'any.required': 'Team member name is required',
    }),
  seafarer_rank: Joi.string()
    .required()
    .messages({
      ...messages,
      'any.required': 'Team member rank is required',
    }),
  seafarer_rank_id: Joi.number().optional(),
  seafarer_rank_sort_order: Joi.number().optional(),
});

const categorySchema = Joi.object<IRiskCategory>({
  is_other: Joi.boolean().required().messages(messages),
  category_id: Joi.alternatives().conditional('is_other', {
    is: false,
    then: Joi.array()
      .items(Joi.number())
      .min(1)
      .required()
      .messages({
        ...messages,
        'any.required': 'Category IDs are required when "is_other" is false',
        'array.base': 'Category IDs must be an array',
      }),
    otherwise: Joi.array().items(Joi.number()).min(0).optional(),
  }),
  value: Joi.string().when('is_other', {
    is: true,
    then: Joi.required().messages({
      'any.required': 'Category name is required when adding a new category',
    }),
    otherwise: Joi.optional(),
  }),
});

const hazardSchema = Joi.object<IRiskHazard>({
  is_other: Joi.boolean().required().messages(messages),
  hazard_id: Joi.alternatives().conditional('is_other', {
    is: false,
    then: Joi.array()
      .items(Joi.number())
      .min(1)
      .required()
      .messages({
        ...messages,
        'any.required': 'Hazard IDs are required when "is_other" is false',
        'array.base': 'Hazard IDs must be an array',
      }),
    otherwise: Joi.array().items(Joi.number()).min(0).optional(),
  }),
  value: Joi.string().when('is_other', {
    is: true,
    then: Joi.required().messages({
      'any.required': 'Hazard name is required when adding a new hazard',
    }),
    otherwise: Joi.optional(),
  }),
});

const reliabilityAssessmentSchema = Joi.object<IRiskTaskReliabilityAssessment>({
  task_reliability_assessment_id: Joi.number().required().messages({
    'any.required': 'Task reliability assessment ID is required',
    'number.base': 'Task reliability assessment ID must be a number',
  }),
  task_reliability_assessment_answer: Joi.string()
    .valid('Yes', 'No', 'NA')
    .insensitive()
    .required()
    .messages({
      'any.required': 'Task reliability assessment answer is required',
      'string.base': 'Task reliability assessment answer must be a string',
      'any.only': 'Answer must be Yes, No, or NA',
    }),
  condition: Joi.alternatives().conditional(
    'task_reliability_assessment_answer',
    {
      is: Joi.string().valid('yes').insensitive(),
      then: Joi.string().max(255).required().messages({
        'any.required': 'Condition is required when the answer is yes',
        'string.base': 'Condition must be a string',
      }),
      otherwise: Joi.forbidden().messages({
        'any.unknown': 'Condition is only allowed when the answer is yes',
      }),
    },
  ),
});

const parameterSchema = Joi.object<IRiskParameter>({
  is_other: Joi.boolean().required().messages(messages),
  parameter_type_id: Joi.number()
    .required()
    .messages({
      ...messages,
      'any.required': 'Parameter type is required',
    }),
  parameter_id: Joi.alternatives().conditional('is_other', {
    is: false,
    then: Joi.array()
      .items(Joi.number())
      .min(1)
      .required()
      .messages({
        ...messages,
        'any.required': 'Parameter IDs are required when "is_other" is false',
        'array.base': 'Parameter IDs must be an array',
      }),
    otherwise: Joi.array().items(Joi.number()).min(0).optional(),
  }),
  value: Joi.string().when('is_other', {
    is: true,
    then: Joi.required().messages({
      'any.required': 'Parameter name is required when adding a new parameter',
    }),
    otherwise: Joi.optional(),
  }),
});

/**
 * Validates vessel details by fetching ownership details from PARIS2 API
 * @param vesselOwnershipId - The vessel ownership ID to validate
 * @returns A tuple containing: [errors, vesselDetails] where errors is a string array of validation errors (empty if valid)
 * and vesselDetails contains the validated vessel information
 */
export const validateRiskVesselDetails = async (
  vesselOwnershipId: number,
): Promise<[string[], any]> => {
  try {
    const vesselOwnership =
      await paris2Controller.getVesselOwnershipById(vesselOwnershipId);

    if (!vesselOwnership) {
      return [['Vessel ownership details not found'], null];
    }

    if (!vesselOwnership.vessel) {
      return [['Vessel details not found in ownership data'], null];
    }

    // Extract required vessel details from API response
    const vesselDetails = {
      vessel_id: vesselOwnership.vessel.id,
      vessel_code: vesselOwnership.vessel_account_code_new,
      vessel_name: vesselOwnership.name,
      vessel_tech_group: vesselOwnership.fleet_staff?.tech_group,
      vessel_category: vesselOwnership.vessel_type?.value,
    };

    // Validate required fields are present
    const errors: string[] = [];

    if (!vesselDetails.vessel_code) {
      errors.push('Vessel code not found in ownership details');
    }
    if (!vesselDetails.vessel_name) {
      errors.push('Vessel name not found in ownership details');
    }
    if (!vesselDetails.vessel_tech_group) {
      errors.push('Vessel tech group not found in ownership details');
    }
    if (!vesselDetails.vessel_category) {
      errors.push('Vessel category not found in ownership details');
    }
    if (!vesselDetails.vessel_code) {
      errors.push('Vessel code not found in ownership details');
    }
    if (!vesselDetails.vessel_name) {
      errors.push('Vessel name not found in ownership details');
    }
    if (!vesselDetails.vessel_tech_group) {
      errors.push('Vessel tech group not found in ownership details');
    }
    if (!vesselDetails.vessel_category) {
      errors.push('Vessel category not found in ownership details');
    }

    return [errors, errors.length === 0 ? vesselDetails : null];
  } catch (error) {
    return [['Failed to validate vessel details'], null];
  }
};

/**
 * Validates team members by comparing with the crew list
 * @param vesselId - The vessel ID to get crew list for
 * @param teamMembers - The team members to validate
 * @returns A string array of validation errors, empty if valid
 */
export const validateRiskTeamMembers = async (
  vesselId: number,
  teamMembers: IRiskTeamMember[],
): Promise<string[]> => {
  try {
    const crewList = await paris2Controller.getCrewList(vesselId);
    if (!crewList || !Array.isArray(crewList)) {
      return ['Failed to fetch crew list'];
    }

    const errors: string[] = [];
    const crewMap = new Map(crewList.map(crew => [crew.seafarer_id, crew]));

    for (const member of teamMembers) {
      if (!member.seafarer_id) {
        errors.push(
          `Seafarer ID is required for team member ${member.seafarer_name}`,
        );
        continue;
      }

      const crew = crewMap.get(member.seafarer_id);
      if (!crew) {
        errors.push(
          `Team member (ID: ${member.seafarer_id}) is not in the crew list`,
        );
      }
    }

    return errors;
  } catch (error) {
    return ['Failed to validate team members'];
  }
};

export const validateRisk = (
  risk: IRiskCreate,
  isPublish: boolean,
): string[] => {
  const baseSchema = {
    task_requiring_ra: Joi.string().messages({
      ...messages,
      'any.required': 'Task requiring risk assessment is required',
    }),
    template_id: Joi.number().min(1).optional(),
    assessor: Joi.number()
      .valid(1, 2)
      .required()
      .messages({
        ...messages,
        'any.required': 'Assessor information is required',
        'any.only': 'Assessor must be either 1 (office) or 2 (vessel)',
      }),
    vessel_ownership_id: Joi.number().when('assessor', {
      is: 2,
      then: Joi.required().messages({
        ...messages,
        'any.required':
          'Vessel ownership ID is required when assessor is 2 (vessel)',
      }),
      otherwise: Joi.optional(),
    }),
    office_id: Joi.number().when('assessor', {
      is: 1,
      then: Joi.required().messages({
        ...messages,
        'any.required': 'Office ID is required when assessor is 1 (office)',
      }),
      otherwise: Joi.optional(),
    }),
    office_name: Joi.string().when('assessor', {
      is: 1,
      then: Joi.required().messages({
        ...messages,
        'any.required': 'Office name is required when assessor is 1 (office)',
      }),
      otherwise: Joi.optional(),
    }),
    date_risk_assessment: Joi.date().optional(),
    task_duration: Joi.string().messages({
      ...messages,
      'any.required': 'Task duration is required',
    }),
    task_alternative_consideration: Joi.string().allow(''),
    task_rejection_reason: Joi.string().allow(''),
    worst_case_scenario: Joi.string().messages({
      ...messages,
      'any.required': 'Worst case scenario must be specified when publishing',
    }),
    recovery_measures: Joi.string().messages({
      ...messages,
      'any.required': 'Recovery measures must be specified when publishing',
    }),
    status: Joi.string()
      .valid('DRAFT', 'PUBLISHED')
      .required()
      .messages({
        ...messages,
        'any.only': 'Status must be either DRAFT or PUBLISHED',
      }),
    risk_team_member: Joi.array()
      .items(teamMemberSchema)
      .messages({
        ...messages,
        'any.required': 'At least one team member is required when publishing',
      }),
    risk_category: categorySchema.messages({
      ...messages,
      'any.required': 'Risk category information is required when publishing',
    }),
    risk_hazard: hazardSchema.messages({
      ...messages,
      'any.required': 'Risk hazard information is required when publishing',
    }),
    parameters: Joi.array()
      .items(parameterSchema)
      .messages({
        ...messages,
        'any.required': 'Parameters must be specified when publishing',
      }),
    risk_job: Joi.array()
      .items(jobSchema)
      .messages({
        ...messages,
        'any.required': 'At least one job must be specified when publishing',
      }),
    risk_task_reliability_assessment: Joi.array()
      .items(reliabilityAssessmentSchema)
      .messages({
        ...messages,
        'any.required':
          'Task reliability assessment is required when publishing',
      }),
    draft_step: Joi.number()
      .min(1)
      .max(8)
      .strict()
      .when('status', {
        is: 'DRAFT',
        then: Joi.required().messages({
          ...messages,
          'any.required': 'draft_step is required when status is DRAFT',
        }),
        otherwise: Joi.optional(),
      })
      .messages({
        ...messages,
        'number.base': 'draft_step must be number',
        'number.min': 'draft_step is allowed between 1 to 8',
        'number.max': 'draft_step is allowed between 1 to 8',
      }),

    approval_required: Joi.array()
      .items(
        Joi.number().required().messages({
          'any.required': 'Approval Required is required',
          'number.base': 'Approval Required must be a number',
        }),
      )
      .optional()
      .messages({
        'array.base': 'approval_required must be an array of numbers',
        'any.required': 'approval_required is required',
      }),
  };

  // When publishing, make all fields required except optional ones
  const schema = isPublish
    ? Joi.object<IRiskCreate>(baseSchema).fork(
        Object.keys(baseSchema).filter(
          key =>
            ![
              'template_id',
              'vessel_ownership_id',
              'vessel_id',
              'vessel_code',
              'vessel_name',
              'office_id',
              'office_name',
              'date_risk_assessment',
              'task_alternative_consideration',
              'task_rejection_reason',
            ].includes(key),
        ),
        schema => schema.required(),
      )
    : Joi.object<IRiskCreate>(baseSchema);

  const {error} = schema.validate(risk, {abortEarly: false});
  return error ? error.details.map(detail => detail.message) : [];
};
